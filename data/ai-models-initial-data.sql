-- AI模型初始数据（包含多语言支持）
-- 插入带有多语言内容的AI模型数据

INSERT INTO ai_models (
  model_id, model_name, model_type, provider, api_endpoint, credits_per_unit, unit_type, 
  description, description_i18n, model_name_i18n, max_input_size, supported_features, is_active
) VALUES
-- Gemini 系列
('gemini-2.5-pro', 'Gemini 2.5 Pro', 'text', 'grsai', '/api/ai/generate', 10, 'tokens', 
 '高级对话模型，适合复杂任务和专业用途',
 '{"en": "Advanced conversational model for complex tasks and professional use", "zh": "高级对话模型，适合复杂任务和专业用途", "ja": "複雑なタスクと専門用途に適した高度な対話モデル"}',
 '{"en": "Gemini 2.5 Pro", "zh": "Gemini 2.5 专业版", "ja": "Gemini 2.5 プロ"}',
 128000, '["text_generation", "conversation", "analysis"]', true),

('gemini-2.5-flash', 'Gemini 2.5 Flash', 'text', 'grsai', '/api/ai/generate', 5, 'tokens',
 '快速对话模型，响应迅速，效率高',
 '{"en": "Fast conversational model with quick responses and high efficiency", "zh": "快速对话模型，响应迅速，效率高", "ja": "迅速な応答と高効率を持つ高速対話モデル"}',
 '{"en": "Gemini 2.5 Flash", "zh": "Gemini 2.5 闪电版", "ja": "Gemini 2.5 フラッシュ"}',
 128000, '["text_generation", "conversation", "fast_response"]', true),

('gemini-2.5-flash-lite', 'Gemini 2.5 Flash Lite', 'text', 'grsai', '/api/ai/generate', 2, 'tokens',
 '轻量级对话模型，成本低廉，功能基础',
 '{"en": "Lightweight conversational model with low cost and basic features", "zh": "轻量级对话模型，成本低廉，功能基础", "ja": "低コストで基本機能を持つ軽量対話モデル"}',
 '{"en": "Gemini 2.5 Flash Lite", "zh": "Gemini 2.5 轻量版", "ja": "Gemini 2.5 ライト"}',
 64000, '["text_generation", "basic_conversation"]', true),

-- GPT-4o 系列
('gpt-4o-mini', 'GPT-4o Mini', 'text', 'grsai', '/api/ai/generate', 8, 'tokens',
 'GPT-4o 轻量版本，性能与成本平衡',
 '{"en": "Compact version of GPT-4o with balanced performance and cost", "zh": "GPT-4o 轻量版本，性能与成本平衡", "ja": "パフォーマンスとコストのバランスが取れたGPT-4oコンパクト版"}',
 '{"en": "GPT-4o Mini", "zh": "GPT-4o 迷你版", "ja": "GPT-4o ミニ"}',
 128000, '["text_generation", "conversation", "reasoning"]', true),

('o4-mini-all', 'GPT-4o Mini All', 'multimodal', 'grsai', '/api/ai/generate', 12, 'tokens',
 'GPT-4o Mini 全功能版本，支持视觉等多模态能力',
 '{"en": "GPT-4o Mini with full multimodal capabilities including vision", "zh": "GPT-4o Mini 全功能版本，支持视觉等多模态能力", "ja": "ビジョンを含むマルチモーダル機能を持つGPT-4o Mini完全版"}',
 '{"en": "GPT-4o Mini All", "zh": "GPT-4o 迷你全功能版", "ja": "GPT-4o ミニ オール"}',
 128000, '["text_generation", "vision", "multimodal", "reasoning"]', true),

('gpt-4o-all', 'GPT-4o All', 'multimodal', 'grsai', '/api/ai/generate', 20, 'tokens',
 'GPT-4o 完整版本，具备所有高级功能和多模态支持',
 '{"en": "Complete GPT-4o with all advanced features and multimodal support", "zh": "GPT-4o 完整版本，具备所有高级功能和多模态支持", "ja": "すべての高度機能とマルチモーダルサポートを持つ完全なGPT-4o"}',
 '{"en": "GPT-4o All", "zh": "GPT-4o 全功能版", "ja": "GPT-4o オール"}',
 128000, '["text_generation", "vision", "multimodal", "advanced_reasoning", "code_generation"]', true),

-- 图像生成模型
('sora-image', 'Sora Image', 'image', 'grsai', '/api/ai/generate', 50, 'images',
 '基于 Sora 技术的先进图像生成模型',
 '{"en": "Advanced image generation model powered by Sora technology", "zh": "基于 Sora 技术的先进图像生成模型", "ja": "Sora技術による先進的な画像生成モデル"}',
 '{"en": "Sora Image", "zh": "Sora 图像生成", "ja": "Sora 画像生成"}',
 4000, '["image_generation", "high_quality", "creative"]', true),

('gpt-4o-image', 'GPT-4o Image', 'image', 'grsai', '/api/ai/generate', 40, 'images',
 '使用 GPT-4o 架构的高质量图像生成',
 '{"en": "High-quality image generation using GPT-4o architecture", "zh": "使用 GPT-4o 架构的高质量图像生成", "ja": "GPT-4oアーキテクチャを使用した高品質画像生成"}',
 '{"en": "GPT-4o Image", "zh": "GPT-4o 图像生成", "ja": "GPT-4o 画像生成"}',
 4000, '["image_generation", "gpt_powered", "versatile"]', true),

-- Flux 系列
('flux-pro-1.1', 'Flux Pro 1.1', 'image', 'grsai', '/api/ai/generate', 30, 'images',
 'Flux 技术 v1.1 专业图像生成',
 '{"en": "Professional image generation with Flux technology v1.1", "zh": "Flux 技术 v1.1 专业图像生成", "ja": "Flux技術v1.1によるプロフェッショナル画像生成"}',
 '{"en": "Flux Pro 1.1", "zh": "Flux 专业版 1.1", "ja": "Flux プロ 1.1"}',
 4000, '["image_generation", "professional", "flux_tech"]', true),

('flux-pro-1.1-ultra', 'Flux Pro 1.1 Ultra', 'image', 'grsai', '/api/ai/generate', 60, 'images',
 '增强版 Flux Pro 超高质量图像生成',
 '{"en": "Ultra-high quality image generation with enhanced Flux Pro", "zh": "增强版 Flux Pro 超高质量图像生成", "ja": "強化されたFlux Proによる超高品質画像生成"}',
 '{"en": "Flux Pro 1.1 Ultra", "zh": "Flux 超级版 1.1", "ja": "Flux ウルトラ 1.1"}',
 4000, '["image_generation", "ultra_quality", "enhanced_flux"]', true),

('flux-kontext-pro', 'Flux Kontext Pro', 'image', 'grsai', '/api/ai/generate', 45, 'images',
 '上下文感知的专业级图像生成',
 '{"en": "Context-aware image generation with professional quality", "zh": "上下文感知的专业级图像生成", "ja": "コンテキスト認識によるプロフェッショナル品質画像生成"}',
 '{"en": "Flux Kontext Pro", "zh": "Flux 上下文专业版", "ja": "Flux コンテキスト プロ"}',
 4000, '["image_generation", "context_aware", "professional"]', true),

('flux-kontext-max', 'Flux Kontext Max', 'image', 'grsai', '/api/ai/generate', 80, 'images',
 '最高质量的上下文感知图像生成',
 '{"en": "Maximum quality context-aware image generation", "zh": "最高质量的上下文感知图像生成", "ja": "最高品質のコンテキスト認識画像生成"}',
 '{"en": "Flux Kontext Max", "zh": "Flux 上下文最大版", "ja": "Flux コンテキスト マックス"}',
 4000, '["image_generation", "max_quality", "context_aware"]', true),

-- 视频生成模型
('veo3-fast', 'Veo3 Fast', 'video', 'grsai', '/api/ai/generate', 100, 'videos',
 'Veo3 技术快速视频生成，结果迅速',
 '{"en": "Fast video generation with Veo3 technology for quick results", "zh": "Veo3 技术快速视频生成，结果迅速", "ja": "迅速な結果を得るVeo3技術による高速動画生成"}',
 '{"en": "Veo3 Fast", "zh": "Veo3 快速版", "ja": "Veo3 ファスト"}',
 2000, '["video_generation", "fast", "veo3_tech"]', true),

('veo3-pro', 'Veo3 Pro', 'video', 'grsai', '/api/ai/generate', 200, 'videos',
 '具备高级 Veo3 能力的专业视频生成',
 '{"en": "Professional video generation with advanced Veo3 capabilities", "zh": "具备高级 Veo3 能力的专业视频生成", "ja": "高度なVeo3機能を持つプロフェッショナル動画生成"}',
 '{"en": "Veo3 Pro", "zh": "Veo3 专业版", "ja": "Veo3 プロ"}',
 2000, '["video_generation", "professional", "advanced_veo3"]', true);

-- 为所有模型添加其他语言的基础翻译
UPDATE ai_models 
SET 
  model_name_i18n = model_name_i18n || jsonb_build_object(
    'ko', model_name_i18n->>'en',
    'fr', model_name_i18n->>'en',
    'de', model_name_i18n->>'en',
    'es', model_name_i18n->>'en',
    'it', model_name_i18n->>'en',
    'pt', model_name_i18n->>'en',
    'ru', model_name_i18n->>'en'
  ),
  description_i18n = description_i18n || jsonb_build_object(
    'ko', description_i18n->>'en',
    'fr', description_i18n->>'en',
    'de', description_i18n->>'en',
    'es', description_i18n->>'en',
    'it', description_i18n->>'en',
    'pt', description_i18n->>'en',
    'ru', description_i18n->>'en'
  )
WHERE model_name_i18n IS NOT NULL AND description_i18n IS NOT NULL;
