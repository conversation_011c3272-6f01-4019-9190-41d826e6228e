-- 插入多语言模型数据
-- 为所有AI模型添加完整的多语言支持

-- 更新现有模型的多语言名称和描述
UPDATE ai_models 
SET 
  model_name_i18n = jsonb_build_object(
    'en', CASE model_id
      WHEN 'gemini-2.5-pro' THEN 'Gemini 2.5 Pro'
      WHEN 'gemini-2.5-flash' THEN 'Gemini 2.5 Flash'
      WHEN 'gemini-2.5-flash-lite' THEN 'Gemini 2.5 Flash Lite'
      WHEN 'gpt-4o-mini' THEN 'GPT-4o Mini'
      WHEN 'o4-mini-all' THEN 'GPT-4o Mini All'
      WHEN 'gpt-4o-all' THEN 'GPT-4o All'
      WHEN 'sora-image' THEN 'Sora Image'
      WHEN 'gpt-4o-image' THEN 'GPT-4o Image'
      WHEN 'flux-pro-1.1' THEN 'Flux Pro 1.1'
      WHEN 'flux-pro-1.1-ultra' THEN 'Flux Pro 1.1 Ultra'
      WHEN 'flux-kontext-pro' THEN 'Flux Kontext Pro'
      WHEN 'flux-kontext-max' THEN 'Flux Kontext Max'
      WHEN 'veo3-fast' THEN 'Veo3 Fast'
      WHEN 'veo3-pro' THEN 'Veo3 Pro'
      ELSE model_name
    END,
    'zh', CASE model_id
      WHEN 'gemini-2.5-pro' THEN 'Gemini 2.5 专业版'
      WHEN 'gemini-2.5-flash' THEN 'Gemini 2.5 闪电版'
      WHEN 'gemini-2.5-flash-lite' THEN 'Gemini 2.5 轻量版'
      WHEN 'gpt-4o-mini' THEN 'GPT-4o 迷你版'
      WHEN 'o4-mini-all' THEN 'GPT-4o 迷你全功能版'
      WHEN 'gpt-4o-all' THEN 'GPT-4o 全功能版'
      WHEN 'sora-image' THEN 'Sora 图像生成'
      WHEN 'gpt-4o-image' THEN 'GPT-4o 图像生成'
      WHEN 'flux-pro-1.1' THEN 'Flux 专业版 1.1'
      WHEN 'flux-pro-1.1-ultra' THEN 'Flux 超级版 1.1'
      WHEN 'flux-kontext-pro' THEN 'Flux 上下文专业版'
      WHEN 'flux-kontext-max' THEN 'Flux 上下文最大版'
      WHEN 'veo3-fast' THEN 'Veo3 快速版'
      WHEN 'veo3-pro' THEN 'Veo3 专业版'
      ELSE model_name
    END,
    'ja', CASE model_id
      WHEN 'gemini-2.5-pro' THEN 'Gemini 2.5 プロ'
      WHEN 'gemini-2.5-flash' THEN 'Gemini 2.5 フラッシュ'
      WHEN 'gemini-2.5-flash-lite' THEN 'Gemini 2.5 ライト'
      WHEN 'gpt-4o-mini' THEN 'GPT-4o ミニ'
      WHEN 'o4-mini-all' THEN 'GPT-4o ミニ オール'
      WHEN 'gpt-4o-all' THEN 'GPT-4o オール'
      WHEN 'sora-image' THEN 'Sora 画像生成'
      WHEN 'gpt-4o-image' THEN 'GPT-4o 画像生成'
      WHEN 'flux-pro-1.1' THEN 'Flux プロ 1.1'
      WHEN 'flux-pro-1.1-ultra' THEN 'Flux ウルトラ 1.1'
      WHEN 'flux-kontext-pro' THEN 'Flux コンテキスト プロ'
      WHEN 'flux-kontext-max' THEN 'Flux コンテキスト マックス'
      WHEN 'veo3-fast' THEN 'Veo3 ファスト'
      WHEN 'veo3-pro' THEN 'Veo3 プロ'
      ELSE model_name
    END
  ),
  description_i18n = jsonb_build_object(
    'en', CASE model_id
      WHEN 'gemini-2.5-pro' THEN 'Advanced conversational model for complex tasks and professional use'
      WHEN 'gemini-2.5-flash' THEN 'Fast conversational model with quick responses and high efficiency'
      WHEN 'gemini-2.5-flash-lite' THEN 'Lightweight conversational model with low cost and basic features'
      WHEN 'gpt-4o-mini' THEN 'Compact version of GPT-4o with balanced performance and cost'
      WHEN 'o4-mini-all' THEN 'GPT-4o Mini with full multimodal capabilities including vision'
      WHEN 'gpt-4o-all' THEN 'Complete GPT-4o with all advanced features and multimodal support'
      WHEN 'sora-image' THEN 'Advanced image generation model powered by Sora technology'
      WHEN 'gpt-4o-image' THEN 'High-quality image generation using GPT-4o architecture'
      WHEN 'flux-pro-1.1' THEN 'Professional image generation with Flux technology v1.1'
      WHEN 'flux-pro-1.1-ultra' THEN 'Ultra-high quality image generation with enhanced Flux Pro'
      WHEN 'flux-kontext-pro' THEN 'Context-aware image generation with professional quality'
      WHEN 'flux-kontext-max' THEN 'Maximum quality context-aware image generation'
      WHEN 'veo3-fast' THEN 'Fast video generation with Veo3 technology for quick results'
      WHEN 'veo3-pro' THEN 'Professional video generation with advanced Veo3 capabilities'
      ELSE description
    END,
    'zh', CASE model_id
      WHEN 'gemini-2.5-pro' THEN '高级对话模型，适合复杂任务和专业用途'
      WHEN 'gemini-2.5-flash' THEN '快速对话模型，响应迅速，效率高'
      WHEN 'gemini-2.5-flash-lite' THEN '轻量级对话模型，成本低廉，功能基础'
      WHEN 'gpt-4o-mini' THEN 'GPT-4o 轻量版本，性能与成本平衡'
      WHEN 'o4-mini-all' THEN 'GPT-4o Mini 全功能版本，支持视觉等多模态能力'
      WHEN 'gpt-4o-all' THEN 'GPT-4o 完整版本，具备所有高级功能和多模态支持'
      WHEN 'sora-image' THEN '基于 Sora 技术的先进图像生成模型'
      WHEN 'gpt-4o-image' THEN '使用 GPT-4o 架构的高质量图像生成'
      WHEN 'flux-pro-1.1' THEN 'Flux 技术 v1.1 专业图像生成'
      WHEN 'flux-pro-1.1-ultra' THEN '增强版 Flux Pro 超高质量图像生成'
      WHEN 'flux-kontext-pro' THEN '上下文感知的专业级图像生成'
      WHEN 'flux-kontext-max' THEN '最高质量的上下文感知图像生成'
      WHEN 'veo3-fast' THEN 'Veo3 技术快速视频生成，结果迅速'
      WHEN 'veo3-pro' THEN '具备高级 Veo3 能力的专业视频生成'
      ELSE description
    END,
    'ja', CASE model_id
      WHEN 'gemini-2.5-pro' THEN '複雑なタスクと専門用途に適した高度な対話モデル'
      WHEN 'gemini-2.5-flash' THEN '迅速な応答と高効率を持つ高速対話モデル'
      WHEN 'gemini-2.5-flash-lite' THEN '低コストで基本機能を持つ軽量対話モデル'
      WHEN 'gpt-4o-mini' THEN 'パフォーマンスとコストのバランスが取れたGPT-4oコンパクト版'
      WHEN 'o4-mini-all' THEN 'ビジョンを含むマルチモーダル機能を持つGPT-4o Mini完全版'
      WHEN 'gpt-4o-all' THEN 'すべての高度機能とマルチモーダルサポートを持つ完全なGPT-4o'
      WHEN 'sora-image' THEN 'Sora技術による先進的な画像生成モデル'
      WHEN 'gpt-4o-image' THEN 'GPT-4oアーキテクチャを使用した高品質画像生成'
      WHEN 'flux-pro-1.1' THEN 'Flux技術v1.1によるプロフェッショナル画像生成'
      WHEN 'flux-pro-1.1-ultra' THEN '強化されたFlux Proによる超高品質画像生成'
      WHEN 'flux-kontext-pro' THEN 'コンテキスト認識によるプロフェッショナル品質画像生成'
      WHEN 'flux-kontext-max' THEN '最高品質のコンテキスト認識画像生成'
      WHEN 'veo3-fast' THEN '迅速な結果を得るVeo3技術による高速動画生成'
      WHEN 'veo3-pro' THEN '高度なVeo3機能を持つプロフェッショナル動画生成'
      ELSE description
    END
  ),
  updated_at = NOW()
WHERE model_id IN (
  'gemini-2.5-pro', 'gemini-2.5-flash', 'gemini-2.5-flash-lite', 'gpt-4o-mini',
  'o4-mini-all', 'gpt-4o-all', 'sora-image', 'gpt-4o-image',
  'flux-pro-1.1', 'flux-pro-1.1-ultra', 'flux-kontext-pro', 'flux-kontext-max',
  'veo3-fast', 'veo3-pro'
);

-- 为其他语言添加基础翻译（可以后续通过管理界面完善）
UPDATE ai_models 
SET 
  model_name_i18n = model_name_i18n || jsonb_build_object(
    'ko', model_name_i18n->>'en',  -- 韩语暂时使用英文
    'fr', model_name_i18n->>'en',  -- 法语暂时使用英文
    'de', model_name_i18n->>'en',  -- 德语暂时使用英文
    'es', model_name_i18n->>'en',  -- 西班牙语暂时使用英文
    'it', model_name_i18n->>'en',  -- 意大利语暂时使用英文
    'pt', model_name_i18n->>'en',  -- 葡萄牙语暂时使用英文
    'ru', model_name_i18n->>'en'   -- 俄语暂时使用英文
  ),
  description_i18n = description_i18n || jsonb_build_object(
    'ko', description_i18n->>'en',
    'fr', description_i18n->>'en',
    'de', description_i18n->>'en',
    'es', description_i18n->>'en',
    'it', description_i18n->>'en',
    'pt', description_i18n->>'en',
    'ru', description_i18n->>'en'
  )
WHERE model_name_i18n IS NOT NULL AND description_i18n IS NOT NULL;

-- 验证数据插入结果
SELECT 
  model_id,
  model_name,
  model_name_i18n->>'en' as name_en,
  model_name_i18n->>'zh' as name_zh,
  model_name_i18n->>'ja' as name_ja,
  description_i18n->>'en' as desc_en,
  description_i18n->>'zh' as desc_zh,
  description_i18n->>'ja' as desc_ja
FROM ai_models 
WHERE is_active = true
ORDER BY model_type, model_id;
