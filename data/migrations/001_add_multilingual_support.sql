-- 添加多语言支持的数据库迁移脚本
-- 将 description 字段改为 JSON 格式以支持多语言

-- 1. 备份现有数据
CREATE TABLE ai_models_backup AS SELECT * FROM ai_models;

-- 2. 添加新的多语言字段
ALTER TABLE ai_models ADD COLUMN description_i18n JSONB;
ALTER TABLE ai_models ADD COLUMN model_name_i18n JSONB;

-- 3. 将现有的中文描述迁移到新的JSON字段
UPDATE ai_models 
SET description_i18n = jsonb_build_object('zh', description)
WHERE description IS NOT NULL;

UPDATE ai_models 
SET model_name_i18n = jsonb_build_object('zh', model_name)
WHERE model_name IS NOT NULL;

-- 4. 添加英文翻译（基于现有数据的英文翻译）
UPDATE ai_models 
SET description_i18n = description_i18n || jsonb_build_object('en', 
  CASE model_id
    WHEN 'gemini-2.5-pro' THEN 'Advanced conversational model for complex tasks'
    WHEN 'gemini-2.5-flash' THEN 'Fast conversational model with quick responses'
    WHEN 'gemini-2.5-flash-lite' THEN 'Lightweight conversational model with low cost'
    WHEN 'gpt-4o-mini' THEN 'GPT-4o lightweight version'
    WHEN 'o4-mini-all' THEN 'GPT-4o Mini full-featured version'
    WHEN 'gpt-4o-all' THEN 'GPT-4o full-featured version'
    WHEN 'sora-image' THEN 'Sora image generation model'
    WHEN 'gpt-4o-image' THEN 'GPT-4o image generation model'
    WHEN 'flux-pro-1.1' THEN 'Flux professional image generation'
    WHEN 'flux-pro-1.1-ultra' THEN 'Flux ultra image generation'
    WHEN 'flux-kontext-pro' THEN 'Flux context professional version'
    WHEN 'flux-kontext-max' THEN 'Flux context maximum version'
    WHEN 'veo3-fast' THEN 'Veo3 fast video generation'
    WHEN 'veo3-pro' THEN 'Veo3 professional video generation'
    ELSE description
  END
)
WHERE description_i18n IS NOT NULL;

UPDATE ai_models 
SET model_name_i18n = model_name_i18n || jsonb_build_object('en', model_name)
WHERE model_name_i18n IS NOT NULL;

-- 5. 为新字段创建索引
CREATE INDEX idx_ai_models_description_i18n ON ai_models USING GIN (description_i18n);
CREATE INDEX idx_ai_models_model_name_i18n ON ai_models USING GIN (model_name_i18n);

-- 6. 创建获取本地化内容的函数
CREATE OR REPLACE FUNCTION get_localized_content(
  content JSONB,
  locale VARCHAR(5) DEFAULT 'en',
  fallback VARCHAR(5) DEFAULT 'zh'
) RETURNS TEXT AS $$
BEGIN
  -- 如果content为空，返回空字符串
  IF content IS NULL THEN
    RETURN '';
  END IF;
  
  -- 尝试获取指定语言的内容
  IF content ? locale THEN
    RETURN content ->> locale;
  END IF;
  
  -- 尝试获取fallback语言的内容
  IF content ? fallback THEN
    RETURN content ->> fallback;
  END IF;
  
  -- 返回第一个可用的值
  RETURN (SELECT value FROM jsonb_each_text(content) LIMIT 1);
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- 7. 创建更新的视图，支持多语言
CREATE OR REPLACE VIEW ai_models_localized AS
SELECT 
  id,
  model_id,
  get_localized_content(model_name_i18n, 'en', 'zh') as model_name_en,
  get_localized_content(model_name_i18n, 'zh', 'en') as model_name_zh,
  model_type,
  provider,
  api_endpoint,
  credits_per_unit,
  unit_type,
  is_active,
  get_localized_content(description_i18n, 'en', 'zh') as description_en,
  get_localized_content(description_i18n, 'zh', 'en') as description_zh,
  description_i18n,
  model_name_i18n,
  max_input_size,
  supported_features,
  created_at,
  updated_at
FROM ai_models;

-- 8. 注释：保留原字段作为兼容性，但标记为废弃
COMMENT ON COLUMN ai_models.description IS 'DEPRECATED: Use description_i18n instead';
COMMENT ON COLUMN ai_models.model_name IS 'DEPRECATED: Use model_name_i18n instead';
COMMENT ON COLUMN ai_models.description_i18n IS 'Multilingual description in JSON format: {"en": "English", "zh": "中文"}';
COMMENT ON COLUMN ai_models.model_name_i18n IS 'Multilingual model name in JSON format: {"en": "English", "zh": "中文"}';
