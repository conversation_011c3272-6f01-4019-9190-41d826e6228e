# AI模型多语言数据库迁移指南

## 概述
这个迁移脚本将为AI模型表添加多语言支持，允许模型名称和描述支持多种语言。

## 迁移内容
- 添加 `description_i18n` 和 `model_name_i18n` JSONB字段
- 迁移现有中文数据到新字段
- 添加英文和日文翻译
- 为其他语言添加基础翻译（使用英文作为占位符）
- 创建本地化内容获取函数

## 执行步骤

### 1. 备份数据库
```bash
# 创建数据库备份
pg_dump your_database > backup_before_multilingual_migration.sql
```

### 2. 执行迁移脚本
```bash
# 方式1：使用psql命令行
psql -d your_database -f scripts/migrate-multilingual.sql

# 方式2：使用Supabase SQL编辑器
# 复制 scripts/migrate-multilingual.sql 的内容到Supabase SQL编辑器中执行
```

### 3. 验证迁移结果
迁移脚本会自动显示验证结果，包括：
- 总模型数量
- 具有多语言名称的模型数量
- 具有多语言描述的模型数量
- 示例数据预览

## 支持的语言
- 英文 (en) - 完整翻译
- 中文 (zh) - 完整翻译  
- 日文 (ja) - 完整翻译
- 韩语 (ko) - 基础翻译（使用英文占位符）
- 法语 (fr) - 基础翻译（使用英文占位符）
- 德语 (de) - 基础翻译（使用英文占位符）
- 西班牙语 (es) - 基础翻译（使用英文占位符）
- 意大利语 (it) - 基础翻译（使用英文占位符）
- 葡萄牙语 (pt) - 基础翻译（使用英文占位符）
- 俄语 (ru) - 基础翻译（使用英文占位符）

## 数据格式示例
```json
{
  "model_name_i18n": {
    "en": "Gemini 2.5 Pro",
    "zh": "Gemini 2.5 专业版",
    "ja": "Gemini 2.5 プロ"
  },
  "description_i18n": {
    "en": "Advanced conversational model for complex tasks",
    "zh": "高级对话模型，适合复杂任务和专业用途",
    "ja": "複雑なタスクと専門用途に適した高度な対話モデル"
  }
}
```

## 后续管理
迁移完成后，可以通过以下方式管理翻译：

1. **管理界面**: 访问 `/admin/translations` 页面
2. **API接口**: 使用 `/api/admin/models/translations` 接口
3. **直接SQL**: 使用 `get_localized_content()` 函数查询

## 回滚方案
如果需要回滚，可以：
1. 恢复备份的数据库
2. 或者删除新添加的字段：
```sql
ALTER TABLE ai_models DROP COLUMN IF EXISTS description_i18n;
ALTER TABLE ai_models DROP COLUMN IF EXISTS model_name_i18n;
DROP FUNCTION IF EXISTS get_localized_content;
```

## 注意事项
- 迁移过程中会自动备份原始数据到 `ai_models_backup` 表
- 原有的 `description` 和 `model_name` 字段保留不变，标记为废弃
- 新的API会自动使用多语言字段，保持向下兼容
- 建议在非生产环境先测试迁移脚本
