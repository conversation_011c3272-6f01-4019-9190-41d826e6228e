-- 完整的多语言数据库迁移脚本
-- 执行顺序：先运行结构迁移，再插入数据

-- ============================================
-- 第一步：结构迁移（来自 001_add_multilingual_support.sql）
-- ============================================

-- 1. 备份现有数据
DROP TABLE IF EXISTS ai_models_backup;
CREATE TABLE ai_models_backup AS SELECT * FROM ai_models;

-- 2. 添加新的多语言字段
ALTER TABLE ai_models ADD COLUMN IF NOT EXISTS description_i18n JSONB;
ALTER TABLE ai_models ADD COLUMN IF NOT EXISTS model_name_i18n JSONB;

-- 3. 将现有的中文描述迁移到新的JSON字段
UPDATE ai_models 
SET description_i18n = jsonb_build_object('zh', description)
WHERE description IS NOT NULL AND description_i18n IS NULL;

UPDATE ai_models 
SET model_name_i18n = jsonb_build_object('zh', model_name)
WHERE model_name IS NOT NULL AND model_name_i18n IS NULL;

-- 4. 为新字段创建索引
CREATE INDEX IF NOT EXISTS idx_ai_models_description_i18n ON ai_models USING GIN (description_i18n);
CREATE INDEX IF NOT EXISTS idx_ai_models_model_name_i18n ON ai_models USING GIN (model_name_i18n);

-- 5. 创建获取本地化内容的函数
CREATE OR REPLACE FUNCTION get_localized_content(
  content JSONB,
  locale VARCHAR(5) DEFAULT 'en',
  fallback VARCHAR(5) DEFAULT 'zh'
) RETURNS TEXT AS $$
BEGIN
  -- 如果content为空，返回空字符串
  IF content IS NULL THEN
    RETURN '';
  END IF;
  
  -- 尝试获取指定语言的内容
  IF content ? locale THEN
    RETURN content ->> locale;
  END IF;
  
  -- 尝试获取fallback语言的内容
  IF content ? fallback THEN
    RETURN content ->> fallback;
  END IF;
  
  -- 返回第一个可用的值
  RETURN (SELECT value FROM jsonb_each_text(content) LIMIT 1);
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- ============================================
-- 第二步：数据插入（来自 002_insert_multilingual_data.sql）
-- ============================================

-- 更新现有模型的多语言名称和描述
UPDATE ai_models 
SET 
  model_name_i18n = jsonb_build_object(
    'en', CASE model_id
      WHEN 'gemini-2.5-pro' THEN 'Gemini 2.5 Pro'
      WHEN 'gemini-2.5-flash' THEN 'Gemini 2.5 Flash'
      WHEN 'gemini-2.5-flash-lite' THEN 'Gemini 2.5 Flash Lite'
      WHEN 'gpt-4o-mini' THEN 'GPT-4o Mini'
      WHEN 'o4-mini-all' THEN 'GPT-4o Mini All'
      WHEN 'gpt-4o-all' THEN 'GPT-4o All'
      WHEN 'sora-image' THEN 'Sora Image'
      WHEN 'gpt-4o-image' THEN 'GPT-4o Image'
      WHEN 'flux-pro-1.1' THEN 'Flux Pro 1.1'
      WHEN 'flux-pro-1.1-ultra' THEN 'Flux Pro 1.1 Ultra'
      WHEN 'flux-kontext-pro' THEN 'Flux Kontext Pro'
      WHEN 'flux-kontext-max' THEN 'Flux Kontext Max'
      WHEN 'veo3-fast' THEN 'Veo3 Fast'
      WHEN 'veo3-pro' THEN 'Veo3 Pro'
      ELSE model_name
    END,
    'zh', CASE model_id
      WHEN 'gemini-2.5-pro' THEN 'Gemini 2.5 专业版'
      WHEN 'gemini-2.5-flash' THEN 'Gemini 2.5 闪电版'
      WHEN 'gemini-2.5-flash-lite' THEN 'Gemini 2.5 轻量版'
      WHEN 'gpt-4o-mini' THEN 'GPT-4o 迷你版'
      WHEN 'o4-mini-all' THEN 'GPT-4o 迷你全功能版'
      WHEN 'gpt-4o-all' THEN 'GPT-4o 全功能版'
      WHEN 'sora-image' THEN 'Sora 图像生成'
      WHEN 'gpt-4o-image' THEN 'GPT-4o 图像生成'
      WHEN 'flux-pro-1.1' THEN 'Flux 专业版 1.1'
      WHEN 'flux-pro-1.1-ultra' THEN 'Flux 超级版 1.1'
      WHEN 'flux-kontext-pro' THEN 'Flux 上下文专业版'
      WHEN 'flux-kontext-max' THEN 'Flux 上下文最大版'
      WHEN 'veo3-fast' THEN 'Veo3 快速版'
      WHEN 'veo3-pro' THEN 'Veo3 专业版'
      ELSE model_name
    END,
    'ja', CASE model_id
      WHEN 'gemini-2.5-pro' THEN 'Gemini 2.5 プロ'
      WHEN 'gemini-2.5-flash' THEN 'Gemini 2.5 フラッシュ'
      WHEN 'gemini-2.5-flash-lite' THEN 'Gemini 2.5 ライト'
      WHEN 'gpt-4o-mini' THEN 'GPT-4o ミニ'
      WHEN 'o4-mini-all' THEN 'GPT-4o ミニ オール'
      WHEN 'gpt-4o-all' THEN 'GPT-4o オール'
      WHEN 'sora-image' THEN 'Sora 画像生成'
      WHEN 'gpt-4o-image' THEN 'GPT-4o 画像生成'
      WHEN 'flux-pro-1.1' THEN 'Flux プロ 1.1'
      WHEN 'flux-pro-1.1-ultra' THEN 'Flux ウルトラ 1.1'
      WHEN 'flux-kontext-pro' THEN 'Flux コンテキスト プロ'
      WHEN 'flux-kontext-max' THEN 'Flux コンテキスト マックス'
      WHEN 'veo3-fast' THEN 'Veo3 ファスト'
      WHEN 'veo3-pro' THEN 'Veo3 プロ'
      ELSE model_name
    END
  ),
  description_i18n = jsonb_build_object(
    'en', CASE model_id
      WHEN 'gemini-2.5-pro' THEN 'Advanced conversational model for complex tasks and professional use'
      WHEN 'gemini-2.5-flash' THEN 'Fast conversational model with quick responses and high efficiency'
      WHEN 'gemini-2.5-flash-lite' THEN 'Lightweight conversational model with low cost and basic features'
      WHEN 'gpt-4o-mini' THEN 'Compact version of GPT-4o with balanced performance and cost'
      WHEN 'o4-mini-all' THEN 'GPT-4o Mini with full multimodal capabilities including vision'
      WHEN 'gpt-4o-all' THEN 'Complete GPT-4o with all advanced features and multimodal support'
      WHEN 'sora-image' THEN 'Advanced image generation model powered by Sora technology'
      WHEN 'gpt-4o-image' THEN 'High-quality image generation using GPT-4o architecture'
      WHEN 'flux-pro-1.1' THEN 'Professional image generation with Flux technology v1.1'
      WHEN 'flux-pro-1.1-ultra' THEN 'Ultra-high quality image generation with enhanced Flux Pro'
      WHEN 'flux-kontext-pro' THEN 'Context-aware image generation with professional quality'
      WHEN 'flux-kontext-max' THEN 'Maximum quality context-aware image generation'
      WHEN 'veo3-fast' THEN 'Fast video generation with Veo3 technology for quick results'
      WHEN 'veo3-pro' THEN 'Professional video generation with advanced Veo3 capabilities'
      ELSE description
    END,
    'zh', CASE model_id
      WHEN 'gemini-2.5-pro' THEN '高级对话模型，适合复杂任务和专业用途'
      WHEN 'gemini-2.5-flash' THEN '快速对话模型，响应迅速，效率高'
      WHEN 'gemini-2.5-flash-lite' THEN '轻量级对话模型，成本低廉，功能基础'
      WHEN 'gpt-4o-mini' THEN 'GPT-4o 轻量版本，性能与成本平衡'
      WHEN 'o4-mini-all' THEN 'GPT-4o Mini 全功能版本，支持视觉等多模态能力'
      WHEN 'gpt-4o-all' THEN 'GPT-4o 完整版本，具备所有高级功能和多模态支持'
      WHEN 'sora-image' THEN '基于 Sora 技术的先进图像生成模型'
      WHEN 'gpt-4o-image' THEN '使用 GPT-4o 架构的高质量图像生成'
      WHEN 'flux-pro-1.1' THEN 'Flux 技术 v1.1 专业图像生成'
      WHEN 'flux-pro-1.1-ultra' THEN '增强版 Flux Pro 超高质量图像生成'
      WHEN 'flux-kontext-pro' THEN '上下文感知的专业级图像生成'
      WHEN 'flux-kontext-max' THEN '最高质量的上下文感知图像生成'
      WHEN 'veo3-fast' THEN 'Veo3 技术快速视频生成，结果迅速'
      WHEN 'veo3-pro' THEN '具备高级 Veo3 能力的专业视频生成'
      ELSE description
    END,
    'ja', CASE model_id
      WHEN 'gemini-2.5-pro' THEN '複雑なタスクと専門用途に適した高度な対話モデル'
      WHEN 'gemini-2.5-flash' THEN '迅速な応答と高効率を持つ高速対話モデル'
      WHEN 'gemini-2.5-flash-lite' THEN '低コストで基本機能を持つ軽量対話モデル'
      WHEN 'gpt-4o-mini' THEN 'パフォーマンスとコストのバランスが取れたGPT-4oコンパクト版'
      WHEN 'o4-mini-all' THEN 'ビジョンを含むマルチモーダル機能を持つGPT-4o Mini完全版'
      WHEN 'gpt-4o-all' THEN 'すべての高度機能とマルチモーダルサポートを持つ完全なGPT-4o'
      WHEN 'sora-image' THEN 'Sora技術による先進的な画像生成モデル'
      WHEN 'gpt-4o-image' THEN 'GPT-4oアーキテクチャを使用した高品質画像生成'
      WHEN 'flux-pro-1.1' THEN 'Flux技術v1.1によるプロフェッショナル画像生成'
      WHEN 'flux-pro-1.1-ultra' THEN '強化されたFlux Proによる超高品質画像生成'
      WHEN 'flux-kontext-pro' THEN 'コンテキスト認識によるプロフェッショナル品質画像生成'
      WHEN 'flux-kontext-max' THEN '最高品質のコンテキスト認識画像生成'
      WHEN 'veo3-fast' THEN '迅速な結果を得るVeo3技術による高速動画生成'
      WHEN 'veo3-pro' THEN '高度なVeo3機能を持つプロフェッショナル動画生成'
      ELSE description
    END
  ),
  updated_at = NOW()
WHERE model_id IN (
  'gemini-2.5-pro', 'gemini-2.5-flash', 'gemini-2.5-flash-lite', 'gpt-4o-mini',
  'o4-mini-all', 'gpt-4o-all', 'sora-image', 'gpt-4o-image',
  'flux-pro-1.1', 'flux-pro-1.1-ultra', 'flux-kontext-pro', 'flux-kontext-max',
  'veo3-fast', 'veo3-pro'
);

-- 为其他语言添加基础翻译（可以后续通过管理界面完善）
UPDATE ai_models 
SET 
  model_name_i18n = model_name_i18n || jsonb_build_object(
    'ko', model_name_i18n->>'en',
    'fr', model_name_i18n->>'en',
    'de', model_name_i18n->>'en',
    'es', model_name_i18n->>'en',
    'it', model_name_i18n->>'en',
    'pt', model_name_i18n->>'en',
    'ru', model_name_i18n->>'en'
  ),
  description_i18n = description_i18n || jsonb_build_object(
    'ko', description_i18n->>'en',
    'fr', description_i18n->>'en',
    'de', description_i18n->>'en',
    'es', description_i18n->>'en',
    'it', description_i18n->>'en',
    'pt', description_i18n->>'en',
    'ru', description_i18n->>'en'
  )
WHERE model_name_i18n IS NOT NULL AND description_i18n IS NOT NULL;

-- ============================================
-- 第三步：验证和清理
-- ============================================

-- 添加字段注释
COMMENT ON COLUMN ai_models.description IS 'DEPRECATED: Use description_i18n instead';
COMMENT ON COLUMN ai_models.model_name IS 'DEPRECATED: Use model_name_i18n instead';
COMMENT ON COLUMN ai_models.description_i18n IS 'Multilingual description in JSON format: {"en": "English", "zh": "中文"}';
COMMENT ON COLUMN ai_models.model_name_i18n IS 'Multilingual model name in JSON format: {"en": "English", "zh": "中文"}';

-- 验证迁移结果
SELECT 
  '=== 迁移完成统计 ===' as status,
  COUNT(*) as total_models,
  COUNT(CASE WHEN model_name_i18n IS NOT NULL THEN 1 END) as models_with_i18n_names,
  COUNT(CASE WHEN description_i18n IS NOT NULL THEN 1 END) as models_with_i18n_descriptions
FROM ai_models 
WHERE is_active = true;

-- 显示示例数据
SELECT 
  model_id,
  model_name_i18n->>'en' as name_en,
  model_name_i18n->>'zh' as name_zh,
  model_name_i18n->>'ja' as name_ja,
  LEFT(description_i18n->>'en', 50) || '...' as desc_en_preview,
  LEFT(description_i18n->>'zh', 50) || '...' as desc_zh_preview
FROM ai_models 
WHERE is_active = true
ORDER BY model_type, model_id
LIMIT 5;
