"use client";

import { useState, useCallback } from "react";

export function useFullscreen() {
  const [isFullscreen, setIsFullscreen] = useState(false);

  const enterFullscreen = useCallback((element?: HTMLElement) => {
    if (element) {
      console.log('进入全屏模式', element);

      // 添加CSS类
      element.classList.add('ai-module-fullscreen');

      // 同时设置内联样式作为备用
      element.style.position = 'fixed';
      element.style.top = '0';
      element.style.left = '0';
      element.style.width = '100vw';
      element.style.height = '100vh';
      element.style.zIndex = '9999';
      element.style.backgroundColor = 'white';
      element.style.overflow = 'auto';
      element.style.padding = '0';  // 优化：移除全屏模式的内边距，直接占满宽度
      element.style.margin = '0';
      element.style.maxWidth = 'none';

      document.body.style.overflow = 'hidden';
      setIsFullscreen(true);
      console.log('CSS类已添加:', element.classList.contains('ai-module-fullscreen'));
      console.log('内联样式已设置');
    }
  }, []);

  const exitFullscreen = useCallback((element?: HTMLElement) => {
    if (element) {
      console.log('退出全屏模式', element);

      // 移除CSS类
      element.classList.remove('ai-module-fullscreen');

      // 清除内联样式
      element.style.position = '';
      element.style.top = '';
      element.style.left = '';
      element.style.width = '';
      element.style.height = '';
      element.style.zIndex = '';
      element.style.backgroundColor = '';
      element.style.overflow = '';
      element.style.padding = '';
      element.style.margin = '';
      element.style.maxWidth = '';

      document.body.style.overflow = '';
      setIsFullscreen(false);
      console.log('全屏模式已退出');
    }
  }, []);

  const toggleFullscreen = useCallback((element?: HTMLElement) => {
    if (isFullscreen) {
      exitFullscreen(element);
    } else {
      enterFullscreen(element);
    }
  }, [isFullscreen, enterFullscreen, exitFullscreen]);

  return {
    isFullscreen,
    isSupported: true, // CSS全屏总是支持的
    enterFullscreen,
    exitFullscreen,
    toggleFullscreen,
  };
}
