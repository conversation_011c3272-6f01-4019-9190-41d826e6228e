import { Metadata } from "next";
import { ModelTranslationManager } from "@/components/admin/ModelTranslationManager";

export const runtime = "edge";

export const metadata: Metadata = {
  title: "Model Translations Management",
  description: "Manage multilingual content for AI models",
};

export default function TranslationsPage() {
  return (
    <div className="container mx-auto py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Model Translations</h1>
        <p className="text-muted-foreground mt-2">
          Manage multilingual content for AI models. Add and edit translations for model names and descriptions.
        </p>
      </div>
      
      <ModelTranslationManager />
    </div>
  );
}
