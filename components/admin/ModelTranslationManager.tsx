"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { CheckCircle, AlertCircle, Globe, Save, RefreshCw } from "lucide-react";
import { toast } from "sonner";
import type { AIModel } from "@/types/ai-model";
import { getLocalizedContent, validateLocalizedContent, updateLocalizedContent } from "@/lib/i18n-content";

interface ModelTranslationManagerProps {
  className?: string;
}

interface TranslationStatus {
  modelId: string;
  modelName: string;
  missingTranslations: {
    name: string[];
    description: string[];
  };
  completeness: number;
}

const SUPPORTED_LOCALES = [
  { code: 'en', name: 'English', flag: '🇺🇸' },
  { code: 'zh', name: '中文', flag: '🇨🇳' },
  { code: 'ja', name: '日本語', flag: '🇯🇵' },
  { code: 'ko', name: '한국어', flag: '🇰🇷' },
  { code: 'fr', name: 'Français', flag: '🇫🇷' },
  { code: 'de', name: 'Deutsch', flag: '🇩🇪' },
  { code: 'es', name: 'Español', flag: '🇪🇸' },
  { code: 'it', name: 'Italiano', flag: '🇮🇹' },
  { code: 'pt', name: 'Português', flag: '🇵🇹' },
  { code: 'ru', name: 'Русский', flag: '🇷🇺' },
];

export function ModelTranslationManager({ className }: ModelTranslationManagerProps) {
  const [models, setModels] = useState<AIModel[]>([]);
  const [translationStatus, setTranslationStatus] = useState<TranslationStatus[]>([]);
  const [selectedModel, setSelectedModel] = useState<AIModel | null>(null);
  const [editingTranslations, setEditingTranslations] = useState<{
    name: Record<string, string>;
    description: Record<string, string>;
  }>({ name: {}, description: {} });
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);

  // 获取模型列表
  const fetchModels = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/ai/models?locale=en');
      const data = await response.json();
      if (data.code === 0) {
        setModels(data.data.models);
        calculateTranslationStatus(data.data.models);
      }
    } catch (error) {
      toast.error('Failed to fetch models');
    } finally {
      setLoading(false);
    }
  };

  // 计算翻译状态
  const calculateTranslationStatus = (modelList: AIModel[]) => {
    const status: TranslationStatus[] = modelList.map(model => {
      const nameTranslations = model.model_name_i18n || {};
      const descriptionTranslations = model.description_i18n || {};
      
      const requiredLocales = SUPPORTED_LOCALES.map(l => l.code);
      const missingNameTranslations = validateLocalizedContent(nameTranslations, requiredLocales);
      const missingDescriptionTranslations = validateLocalizedContent(descriptionTranslations, requiredLocales);
      
      const totalRequired = requiredLocales.length * 2; // name + description
      const totalMissing = missingNameTranslations.length + missingDescriptionTranslations.length;
      const completeness = Math.round(((totalRequired - totalMissing) / totalRequired) * 100);

      return {
        modelId: model.model_id,
        modelName: model.model_name,
        missingTranslations: {
          name: missingNameTranslations,
          description: missingDescriptionTranslations,
        },
        completeness,
      };
    });

    setTranslationStatus(status);
  };

  // 选择模型进行编辑
  const selectModelForEditing = (model: AIModel) => {
    setSelectedModel(model);
    setEditingTranslations({
      name: { ...(model.model_name_i18n || {}) },
      description: { ...(model.description_i18n || {}) },
    });
  };

  // 更新翻译内容
  const updateTranslation = (field: 'name' | 'description', locale: string, value: string) => {
    setEditingTranslations(prev => ({
      ...prev,
      [field]: {
        ...prev[field],
        [locale]: value,
      },
    }));
  };

  // 保存翻译
  const saveTranslations = async () => {
    if (!selectedModel) return;

    setSaving(true);
    try {
      const response = await fetch('/api/admin/models/translations', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          modelId: selectedModel.model_id,
          translations: editingTranslations,
        }),
      });

      const data = await response.json();
      if (data.code === 0) {
        toast.success('Translations saved successfully');
        fetchModels(); // 重新获取数据
      } else {
        toast.error(data.msg || 'Failed to save translations');
      }
    } catch (error) {
      toast.error('Failed to save translations');
    } finally {
      setSaving(false);
    }
  };

  useEffect(() => {
    fetchModels();
  }, []);

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 概览统计 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Models</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{models.length}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Fully Translated</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {translationStatus.filter(s => s.completeness === 100).length}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Needs Translation</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">
              {translationStatus.filter(s => s.completeness < 100).length}
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 模型列表 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Globe className="w-5 h-5" />
              Models Translation Status
            </CardTitle>
            <CardDescription>
              Click on a model to edit its translations
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <RefreshCw className="w-6 h-6 animate-spin" />
              </div>
            ) : (
              translationStatus.map(status => (
                <div
                  key={status.modelId}
                  className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                    selectedModel?.model_id === status.modelId
                      ? 'border-primary bg-primary/5'
                      : 'border-border hover:bg-muted/50'
                  }`}
                  onClick={() => {
                    const model = models.find(m => m.model_id === status.modelId);
                    if (model) selectModelForEditing(model);
                  }}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">{status.modelName}</div>
                      <div className="text-sm text-muted-foreground">{status.modelId}</div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant={status.completeness === 100 ? "default" : "secondary"}>
                        {status.completeness}%
                      </Badge>
                      {status.completeness === 100 ? (
                        <CheckCircle className="w-4 h-4 text-green-600" />
                      ) : (
                        <AlertCircle className="w-4 h-4 text-orange-600" />
                      )}
                    </div>
                  </div>
                </div>
              ))
            )}
          </CardContent>
        </Card>

        {/* 翻译编辑器 */}
        <Card>
          <CardHeader>
            <CardTitle>Translation Editor</CardTitle>
            <CardDescription>
              {selectedModel ? `Editing: ${selectedModel.model_name}` : 'Select a model to edit translations'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {selectedModel ? (
              <Tabs defaultValue="name" className="space-y-4">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="name">Model Name</TabsTrigger>
                  <TabsTrigger value="description">Description</TabsTrigger>
                </TabsList>
                
                <TabsContent value="name" className="space-y-4">
                  {SUPPORTED_LOCALES.map(locale => (
                    <div key={locale.code} className="space-y-2">
                      <Label className="flex items-center gap-2">
                        <span>{locale.flag}</span>
                        <span>{locale.name}</span>
                      </Label>
                      <Input
                        value={editingTranslations.name[locale.code] || ''}
                        onChange={(e) => updateTranslation('name', locale.code, e.target.value)}
                        placeholder={`Model name in ${locale.name}`}
                      />
                    </div>
                  ))}
                </TabsContent>
                
                <TabsContent value="description" className="space-y-4">
                  {SUPPORTED_LOCALES.map(locale => (
                    <div key={locale.code} className="space-y-2">
                      <Label className="flex items-center gap-2">
                        <span>{locale.flag}</span>
                        <span>{locale.name}</span>
                      </Label>
                      <Textarea
                        value={editingTranslations.description[locale.code] || ''}
                        onChange={(e) => updateTranslation('description', locale.code, e.target.value)}
                        placeholder={`Description in ${locale.name}`}
                        rows={3}
                      />
                    </div>
                  ))}
                </TabsContent>
                
                <div className="flex gap-2 pt-4">
                  <Button onClick={saveTranslations} disabled={saving}>
                    {saving ? (
                      <>
                        <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="w-4 h-4 mr-2" />
                        Save Translations
                      </>
                    )}
                  </Button>
                  <Button variant="outline" onClick={fetchModels}>
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Refresh
                  </Button>
                </div>
              </Tabs>
            ) : (
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Select a model from the list to start editing translations.
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
