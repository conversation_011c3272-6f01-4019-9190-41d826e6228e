"use client";

import { useTranslations } from "next-intl";

interface CreditsDisplayProps {
  userCredits: number;
}

export function CreditsDisplay({ userCredits }: CreditsDisplayProps) {
  const t = useTranslations("ai-dashboard.credits");

  return (
    <div className="flex items-center gap-3 pb-0 mb-0 text-muted-foreground bg-gradient-to-r from-muted/30 to-muted/10 rounded-lg border border-border/30 w-full max-w-full overflow-x-hidden">
      <div>
        <span className="text-sm font-medium text-foreground">{t("current_balance")}: {userCredits}</span>
      </div>
    </div>
  );
}
