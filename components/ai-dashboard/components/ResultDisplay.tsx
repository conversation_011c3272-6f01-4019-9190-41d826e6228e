"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import {
  Brain,
  CheckCircle,
  AlertCircle,
  Loader2,
  Eye,
  Download,
  Coins
} from "lucide-react";
import { useTranslations } from "next-intl";
import type { WorkspaceGenerationResult } from "./types";

interface ResultDisplayProps {
  generationResult: WorkspaceGenerationResult | null;
}

export function ResultDisplay({ generationResult }: ResultDisplayProps) {
  const t = useTranslations("ai-dashboard");

  if (!generationResult) {
    return (
      <div className="text-center py-12">
        <div className="p-6 rounded-2xl bg-gradient-to-br from-muted/30 to-muted/10 border border-border/30 max-w-md mx-auto">
          <div className="p-4 rounded-xl bg-gradient-to-r from-primary to-accent mb-4 w-fit mx-auto">
            <Brain className="w-8 h-8 text-primary-foreground" />
          </div>
          <h3 className="text-lg font-semibold text-foreground mb-2">{t("workspace.start_create")}</h3>
          <p className="text-muted-foreground text-sm">
            {t("workspace.choose_model")}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* 生成状态 */}
      <div className="flex items-center gap-3 p-4 rounded-xl bg-gradient-to-r from-muted/30 to-muted/10 border border-border/30">
        <div className={`p-2 rounded-lg ${
          generationResult.status === 'success' ? 'bg-gradient-to-r from-green-500 to-emerald-500' :
          generationResult.status === 'failed' ? 'bg-gradient-to-r from-red-500 to-rose-500' :
          'bg-gradient-to-r from-blue-500 to-purple-500'
        }`}>
          {generationResult.status === 'success' && <CheckCircle className="w-5 h-5 text-white" />}
          {generationResult.status === 'failed' && <AlertCircle className="w-5 h-5 text-white" />}
          {(generationResult.status === 'pending' || generationResult.status === 'running') && <Loader2 className="w-5 h-5 animate-spin text-white" />}
        </div>
        <div>
          <span className="font-semibold text-foreground">
            {generationResult.status === 'success' && '生成完成'}
            {generationResult.status === 'failed' && '生成失败'}
            {(generationResult.status === 'pending' || generationResult.status === 'running') && '生成中...'}
          </span>
          {generationResult.progress !== undefined && (
            <div className="space-y-2">
              <div className="text-sm text-muted-foreground">进度: {generationResult.progress}%</div>
              <Progress value={generationResult.progress} className="w-full" />
            </div>
          )}
        </div>
      </div>

      {/* 结果内容 */}
      {generationResult.status === 'success' && generationResult.result && (
        <div className="space-y-4">
          {/* 文本结果 */}
          {generationResult.result.text && (
            <div className="p-6 bg-gradient-to-br from-muted/20 to-muted/10 rounded-xl border border-border/30 backdrop-blur-sm">
              <pre className="whitespace-pre-wrap text-sm leading-relaxed text-foreground">{generationResult.result.text}</pre>
            </div>
          )}

          {/* 图像结果 */}
          {generationResult.result.images && (
            <div className="grid grid-cols-1 gap-4">
              {generationResult.result.images.map((image, index) => (
                <div key={index} className="space-y-4 p-4 bg-gradient-to-br from-muted/20 to-muted/10 rounded-xl border border-border/30">
                  <img
                    src={image.url}
                    alt={`Generated image ${index + 1}`}
                    className="w-full rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-200"
                  />
                  <div className="flex gap-3">
                    <Button size="sm" variant="outline" asChild className="flex-1">
                      <a href={image.url} target="_blank" rel="noopener noreferrer">
                        <Eye className="w-4 h-4 mr-2" />
                        {t("actions.view")}
                      </a>
                    </Button>
                    <Button size="sm" variant="default" asChild className="flex-1">
                      <a href={image.url} download>
                        <Download className="w-4 h-4 mr-2" />
                        {t("actions.download")}
                      </a>
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* 视频结果 */}
          {generationResult.result.video && (
            <div className="space-y-2">
              <video
                src={generationResult.result.video.url}
                controls
                className="w-full rounded-lg shadow-md"
              />
              <div className="flex gap-2">
                <Button size="sm" variant="outline" asChild>
                  <a href={generationResult.result.video.url} target="_blank" rel="noopener noreferrer">
                    <Eye className="w-4 h-4 mr-1" />
                    View
                  </a>
                </Button>
                <Button size="sm" variant="outline" asChild>
                  <a href={generationResult.result.video.url} download>
                    <Download className="w-4 h-4 mr-1" />
                    Download
                  </a>
                </Button>
              </div>
            </div>
          )}

          {/* 使用统计 */}
          {generationResult.usage && (
            <div className="flex items-center gap-3 p-3 bg-gradient-to-r from-yellow-500/10 to-orange-500/10 rounded-lg border border-yellow-500/20">
              <div className="p-2 rounded-lg bg-gradient-to-r from-yellow-500 to-orange-500">
                <Coins className="w-4 h-4 text-white" />
              </div>
              <span className="text-sm font-medium text-foreground">
                {t("cost.consumed", { amount: generationResult.usage.credits_consumed })}
              </span>
            </div>
          )}
        </div>
      )}

      {/* 错误信息 */}
      {generationResult.status === 'failed' && generationResult.error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {t("errors.generation_failed", { detail: generationResult.error.detail })}
          </AlertDescription>
        </Alert>
      )}

      {/* 进度显示 */}
      {(generationResult.status === 'pending' || generationResult.status === 'running') && (
        <div className="text-center py-4">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-2" />
          <p className="text-sm text-gray-600">
            Generating... please wait...
            {generationResult.progress !== undefined && ` (${generationResult.progress}%)`}
          </p>
        </div>
      )}
    </div>
  );
}
