"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Loader2, Send } from "lucide-react";
import { useDeviceLayout } from "../hooks/use-device-layout";
import { useButtonStyles } from "../hooks/use-responsive-styles";
import { useTranslations } from "next-intl";
import type { AIModel, CostEstimate } from "./types";

interface GenerateButtonProps {
  loading: boolean;
  selectedModel: AIModel | null;
  prompt: string;
  costEstimate: CostEstimate | null;
  onGenerate: () => void;
}

export function GenerateButton({
  loading,
  selectedModel,
  prompt,
  costEstimate,
  onGenerate
}: GenerateButtonProps) {
  const { isMobile } = useDeviceLayout();
  const { className: buttonClassName } = useButtonStyles();
  const t = useTranslations("ai-dashboard.generator");

  const isDisabled = loading ||
    !selectedModel ||
    !prompt.trim() ||
    (costEstimate ? !costEstimate.user_credits.can_afford : false);

  return (
    <Button
      onClick={onGenerate}
      disabled={isDisabled}
      className={`${buttonClassName} w-full max-w-full font-semibold rounded-xl`}
      size="lg"
    >
      {loading ? (
        <>
          <Loader2 className={`${isMobile ? 'w-4 h-4' : 'w-5 h-5'} mr-2 animate-spin`} />
          {t("generating")}
        </>
      ) : (
        <>
          <Send className={`${isMobile ? 'w-4 h-4' : 'w-5 h-5'} mr-2`} />
          {t("start")}
        </>
      )}
    </Button>
  );
}
