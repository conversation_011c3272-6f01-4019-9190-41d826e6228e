"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { MessageSquare, Image, Video } from "lucide-react";
import { useTranslations } from "next-intl";

interface ModelTypeTabsProps {
  activeTab: string;
  onTabChange: (value: string) => void;
}

export function ModelTypeTabs({ activeTab, onTabChange }: ModelTypeTabsProps) {
  const t = useTranslations("ai-dashboard.tabs");

  return (
    <Tabs value={activeTab} onValueChange={onTabChange} className="w-full max-w-full overflow-x-hidden">
      <TabsList className="grid w-full max-w-full grid-cols-3 mb-6 bg-gradient-to-r from-muted/50 to-muted/30">
        <TabsTrigger
          value="text"
          className="flex items-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-purple-500 data-[state=active]:text-white"
        >
          <MessageSquare className="w-4 h-4" />
          {t("text")}
        </TabsTrigger>
        <TabsTrigger
          value="image"
          className="flex items-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-500 data-[state=active]:to-teal-500 data-[state=active]:text-white"
        >
          <Image className="w-4 h-4" />
          {t("image")}
        </TabsTrigger>
        <TabsTrigger
          value="video"
          className="flex items-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-orange-500 data-[state=active]:to-red-500 data-[state=active]:text-white"
        >
          <Video className="w-4 h-4" />
          {t("video")}
        </TabsTrigger>
      </TabsList>
    </Tabs>
  );
}
