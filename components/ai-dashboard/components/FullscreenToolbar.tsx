"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Brain, Minimize } from "lucide-react";
import { useTranslations } from "next-intl";
import { useDeviceLayout } from "../hooks/use-device-layout";

interface FullscreenToolbarProps {
  onExitFullscreen: () => void;
}

export function FullscreenToolbar({ onExitFullscreen }: FullscreenToolbarProps) {
  const t = useTranslations("ai-dashboard");
  const { isMobile } = useDeviceLayout();

  return (
    <div className={`sticky top-0 z-50 bg-background/95 backdrop-blur-md border-b border-border/50 shadow-sm ${
      isMobile ? 'px-0 py-1' : 'px-6 py-4'  // 优化：移动端无水平内边距，直接占满宽度
    }`}>
      <div className={`flex items-center justify-between ${
        isMobile ? 'px-2' : ''  // 优化：移动端内部容器添加最小内边距
      }`}>
        <div className="flex items-center gap-2 md:gap-4">  {/* 优化：移动端减少间距 */}
          <div className={`rounded-xl bg-gradient-to-r from-primary to-accent ${
            isMobile ? 'p-1.5' : 'p-2'  // 优化：移动端减少图标容器大小
          }`}>
            <Brain className={`text-primary-foreground ${
              isMobile ? 'w-4 h-4' : 'w-6 h-6'  // 优化：移动端减少图标大小
            }`} />
          </div>
          <h1 className={`font-semibold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent ${
            isMobile ? 'text-sm' : 'text-xl'  // 优化：移动端减少标题字体大小
          }`}>
            {isMobile ? t("workspace.title") : `${t("workspace.title")} - ${t("workspace.fullscreen")}`}  {/* 优化：移动端简化标题 */}
          </h1>
        </div>
        <Button
          variant="outline"
          size={isMobile ? "sm" : "sm"}  // 保持小尺寸按钮
          onClick={onExitFullscreen}
          className="flex items-center gap-1 md:gap-2 z-50 relative"  // 优化：移动端减少按钮内间距
        >
          <Minimize className="w-4 h-4" />
          {!isMobile && t("toolbar.exit_fullscreen")}  {/* 优化：移动端隐藏按钮文字 */}
        </Button>
      </div>
    </div>
  );
}
