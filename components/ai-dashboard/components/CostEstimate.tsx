"use client";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { useTranslations } from "next-intl";
import type { CostEstimate as CostEstimateType } from "./types";

interface CostEstimateProps {
  costEstimate: CostEstimateType | null;
}

export function CostEstimate({ costEstimate }: CostEstimateProps) {
  const t = useTranslations("ai-dashboard.cost");

  if (!costEstimate) return null;

  return (
    <Alert className="bg-gradient-to-r from-accent/10 to-primary/10 border-border/30 backdrop-blur-sm w-full max-w-full overflow-x-hidden">
      <AlertCircle className="h-4 w-4" />
      <AlertDescription>
        {t("estimated")}: {costEstimate.cost_estimate.estimated_credits} {t("credits")}
        {!costEstimate.user_credits.can_afford && (
          <span className="text-destructive ml-2">
            ({t("not_enough", { shortfall: costEstimate.user_credits.shortfall })})
          </span>
        )}
      </AlertDescription>
    </Alert>
  );
}
