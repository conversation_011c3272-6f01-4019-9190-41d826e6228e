"use client";

import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, Maximize } from "lucide-react";
import { useTranslations } from "next-intl";
import { useDeviceLayout } from "../hooks/use-device-layout";  // 优化：导入设备布局hook

interface WorkspaceHeaderProps {
  onEnterFullscreen: () => void;
}

export function WorkspaceHeader({ onEnterFullscreen }: WorkspaceHeaderProps) {
  const t = useTranslations("ai-dashboard.workspace");
  const { isMobile, isSmallMobile } = useDeviceLayout();  // 优化：获取设备状态

  return (
    <div className={`bg-gradient-to-br from-background/50 via-background to-accent/5 backdrop-blur-sm ${
      isSmallMobile ? 'px-3 py-4' :  // 优化：超小屏幕最小内边距
      isMobile ? 'px-4 py-6' : 'px-6 py-12'
    }`}>
      <div className={`flex items-center justify-between ${
        isSmallMobile ? 'mb-3' :  // 优化：超小屏幕减少下边距
        isMobile ? 'mb-4' : 'mb-8'
      }`}>
        <div className={`flex items-center ${
          isSmallMobile ? 'gap-2' :  // 优化：超小屏幕减少间距
          isMobile ? 'gap-3' : 'gap-4'
        }`}>
          <div className={`rounded-2xl bg-gradient-to-r from-primary to-accent shadow-lg ${
            isSmallMobile ? 'p-1.5' :  // 优化：超小屏幕减少内边距
            isMobile ? 'p-2' : 'p-3'
          }`}>
            <Brain className={`text-primary-foreground ${
              isSmallMobile ? 'w-5 h-5' :  // 优化：超小屏幕减少图标大小
              isMobile ? 'w-6 h-6' : 'w-8 h-8'
            }`} />
          </div>
          <div>
            <h1 className={`font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent ${
              isSmallMobile ? 'text-lg' :  // 优化：超小屏幕减少标题字体大小
              isMobile ? 'text-xl' : 'text-3xl'
            }`}>
              {t("title")}
            </h1>
            {!isSmallMobile && (  // 优化：超小屏幕隐藏副标题
              <p className={`text-muted-foreground ${
                isMobile ? 'mt-0.5 text-sm' : 'mt-1 text-base'
              }`}>{t("subtitle")}</p>
            )}
          </div>
        </div>
        <Button
          variant="outline"
          size={isSmallMobile ? "sm" : isMobile ? "default" : "lg"}  // 优化：响应式按钮大小
          onClick={onEnterFullscreen}
          className={`flex items-center ${isSmallMobile ? 'gap-1' : 'gap-2'}`}  // 优化：响应式间距
        >
          <Maximize className={`${
            isSmallMobile ? 'w-3 h-3' :  // 优化：超小屏幕减少图标大小
            isMobile ? 'w-4 h-4' : 'w-5 h-5'
          }`} />
          {!isSmallMobile && t("fullscreen")}  {/* 优化：超小屏幕隐藏按钮文字 */}
        </Button>
      </div>
    </div>
  );
}
