"use client";

import { useState, useEffect } from 'react';

export type DeviceType = 'mobile' | 'tablet' | 'desktop';
export type LayoutMode = 'mobile' | 'tablet' | 'desktop';

interface DeviceLayoutInfo {
  deviceType: DeviceType;
  layoutMode: LayoutMode;
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isSmallMobile: boolean;  // 优化：新增超小屏幕检测
  screenWidth: number;
  screenHeight: number;
}

const BREAKPOINTS = {
  smallMobile: 375,  // 优化：新增超小屏幕断点
  mobile: 768,
  tablet: 1200,
} as const;

export function useDeviceLayout(): DeviceLayoutInfo {
  const [deviceInfo, setDeviceInfo] = useState<DeviceLayoutInfo>({
    deviceType: 'desktop',
    layoutMode: 'desktop',
    isMobile: false,
    isTablet: false,
    isDesktop: true,
    isSmallMobile: false,  // 优化：添加超小屏幕初始值
    screenWidth: 1920,
    screenHeight: 1080,
  });

  useEffect(() => {
    const updateDeviceInfo = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      
      let deviceType: DeviceType;
      let layoutMode: LayoutMode;
      
      if (width < BREAKPOINTS.mobile) {
        deviceType = 'mobile';
        layoutMode = 'mobile';
      } else if (width < BREAKPOINTS.tablet) {
        deviceType = 'tablet';
        layoutMode = 'tablet';
      } else {
        deviceType = 'desktop';
        layoutMode = 'desktop';
      }

      setDeviceInfo({
        deviceType,
        layoutMode,
        isMobile: deviceType === 'mobile',
        isTablet: deviceType === 'tablet',
        isDesktop: deviceType === 'desktop',
        isSmallMobile: width < BREAKPOINTS.smallMobile,  // 优化：添加超小屏幕检测
        screenWidth: width,
        screenHeight: height,
      });
    };

    // 初始化
    updateDeviceInfo();

    // 监听窗口大小变化
    window.addEventListener('resize', updateDeviceInfo);
    
    return () => {
      window.removeEventListener('resize', updateDeviceInfo);
    };
  }, []);

  return deviceInfo;
}

// 兼容现有代码的简化版本
export function useScreenSize(): 'mobile' | 'tablet' | 'desktop' {
  const { layoutMode } = useDeviceLayout();
  return layoutMode;
}
