/**
 * 多语言内容获取工具函数
 * 用于处理数据库中的JSON格式多语言内容
 */

export type LocalizedContent = Record<string, string> | string | null | undefined;

/**
 * 获取本地化内容
 * @param content 多语言内容对象或字符串
 * @param locale 目标语言代码
 * @param fallback 后备语言代码
 * @returns 本地化后的字符串
 */
export function getLocalizedContent(
  content: LocalizedContent,
  locale: string = 'en',
  fallback: string = 'zh'
): string {
  // 如果内容为空，返回空字符串
  if (!content) {
    return '';
  }

  // 如果是字符串，直接返回（兼容旧数据）
  if (typeof content === 'string') {
    return content;
  }

  // 如果是对象，按优先级获取内容
  if (typeof content === 'object') {
    // 1. 尝试获取目标语言
    if (content[locale]) {
      return content[locale];
    }

    // 2. 尝试获取后备语言
    if (content[fallback]) {
      return content[fallback];
    }

    // 3. 获取第一个可用的值
    const values = Object.values(content).filter(Boolean);
    if (values.length > 0) {
      return values[0];
    }
  }

  return '';
}

/**
 * 创建多语言内容对象
 * @param translations 翻译映射
 * @returns 多语言内容对象
 */
export function createLocalizedContent(translations: Record<string, string>): Record<string, string> {
  return translations;
}

/**
 * 更新多语言内容
 * @param existingContent 现有内容
 * @param locale 语言代码
 * @param newContent 新内容
 * @returns 更新后的多语言内容对象
 */
export function updateLocalizedContent(
  existingContent: LocalizedContent,
  locale: string,
  newContent: string
): Record<string, string> {
  let content: Record<string, string> = {};

  // 如果现有内容是对象，复制它
  if (typeof existingContent === 'object' && existingContent !== null) {
    content = { ...existingContent };
  } else if (typeof existingContent === 'string') {
    // 如果现有内容是字符串，假设它是中文
    content = { zh: existingContent };
  }

  // 更新指定语言的内容
  content[locale] = newContent;

  return content;
}

/**
 * 验证多语言内容是否完整
 * @param content 多语言内容
 * @param requiredLocales 必需的语言列表
 * @returns 缺失的语言列表
 */
export function validateLocalizedContent(
  content: LocalizedContent,
  requiredLocales: string[] = ['en', 'zh']
): string[] {
  if (!content || typeof content !== 'object') {
    return requiredLocales;
  }

  return requiredLocales.filter(locale => !content[locale] || content[locale].trim() === '');
}

/**
 * 获取支持的语言列表
 * @param content 多语言内容
 * @returns 支持的语言代码列表
 */
export function getSupportedLocales(content: LocalizedContent): string[] {
  if (!content || typeof content !== 'object') {
    return [];
  }

  return Object.keys(content).filter(locale => content[locale] && content[locale].trim() !== '');
}

/**
 * React Hook: 获取本地化内容
 * @param content 多语言内容
 * @param locale 当前语言
 * @returns 本地化后的内容
 */
export function useLocalizedContent(content: LocalizedContent, locale?: string): string {
  // 如果没有提供locale，尝试从next-intl获取
  let currentLocale = locale;
  
  if (!currentLocale && typeof window !== 'undefined') {
    // 客户端环境，尝试从URL或localStorage获取
    currentLocale = document.documentElement.lang || 'en';
  }

  return getLocalizedContent(content, currentLocale || 'en');
}

/**
 * 服务端获取本地化内容的辅助函数
 * @param content 多语言内容
 * @param request 请求对象（用于获取Accept-Language）
 * @returns 本地化后的内容
 */
export function getLocalizedContentFromRequest(
  content: LocalizedContent,
  request?: Request
): string {
  let locale = 'en';

  if (request) {
    const acceptLanguage = request.headers.get('Accept-Language');
    if (acceptLanguage) {
      // 简单的语言检测
      if (acceptLanguage.includes('zh')) {
        locale = 'zh';
      } else if (acceptLanguage.includes('en')) {
        locale = 'en';
      }
    }
  }

  return getLocalizedContent(content, locale);
}
